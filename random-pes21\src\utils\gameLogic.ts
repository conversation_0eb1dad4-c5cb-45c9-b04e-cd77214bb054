import { Club, GameResult, Settings } from '../types';
import { getClubs } from '../data/clubs';
import { getMainClubs } from '../data/mainClubs';

const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Top five leagues
const TOP_LEAGUES = ["🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League", "🇪🇸 La Liga", "🇮🇹 Serie A", "🇩🇪 Bundesliga", "🇫🇷 Ligue 1"];

// Function to check if a club is from a top league
const isTopLeagueClub = (club: Club): boolean => {
  return !!club.league && TOP_LEAGUES.includes(club.league);
};

// Weighting factors - increase these to make the effect more noticeable
const TOP_LEAGUE_WEIGHT = 3.0; // Was 1.5, then 2.5
const SAME_LEAGUE_WEIGHT = 3.0; // Was 1.3, then 2.0

const predictScore = (team1: Club, team2: Club): [number, number] => {
  const strengthDiff = (team1.strength || 80) - (team2.strength || 80);
  const baseGoals = 4; // Increased from 2 to 4
  const randomFactor = Math.random() * 3; // Additional random factor

  let team1Goals = Math.max(1, Math.floor(randomFactor + baseGoals + strengthDiff / 8));
  let team2Goals = Math.max(1, Math.floor(randomFactor + baseGoals - strengthDiff / 8));

  // Ensure there's always a winner
  if (team1Goals === team2Goals) {
    if (Math.random() > 0.5) {
      team1Goals++;
    } else {
      team2Goals++;
    }
  }

  return [team1Goals, team2Goals];
};

// Function to select a weighted random club from a list
const selectWeightedRandomClub = (clubs: Club[], weights: number[]): Club => {
  // Calculate the total weight
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

  // Generate a random number between 0 and the total weight
  const randomValue = Math.random() * totalWeight;

  // Find the club that corresponds to the random value
  let weightSum = 0;
  for (let i = 0; i < clubs.length; i++) {
    weightSum += weights[i];
    if (randomValue <= weightSum) {
      return clubs[i];
    }
  }

  // Fallback (should never reach here if weights are properly calculated)
  return clubs[0];
};

export const generateGameData = (settings?: Settings): GameResult => {
  // Get main clubs for player1Club and player2Club
  const mainClubsList = getMainClubs();

  // Get regular clubs for the top 4 teams
  const regularClubs = getClubs(settings);

  // Ensure we have enough main clubs for the two players
  if (mainClubsList.length < 2) {
    throw new Error("Not enough main clubs available. Please add more clubs to the mainClubs list.");
  }

  // Ensure we have enough regular clubs for the top teams
  if (regularClubs.length < 8) {
    throw new Error("Not enough regular clubs available. Please exclude fewer teams in settings.");
  }

  // Assign weights to clubs based on their league
  // Top five leagues get higher weights
  const clubWeights = mainClubsList.map(club =>
    isTopLeagueClub(club) ? TOP_LEAGUE_WEIGHT : 1
  );

  // Select first club with weighted probability favoring top leagues
  const player1Club = selectWeightedRandomClub(mainClubsList, clubWeights);

  // Create weights for the second club selection
  // Clubs from the same league as player1Club get higher weights
  const player2Weights = mainClubsList.map(club => {
    // Base weight
    let weight = 1;

    // Increase weight for top leagues
    if (isTopLeagueClub(club)) {
      weight = TOP_LEAGUE_WEIGHT;
    }

    // Increase weight for clubs from the same league as player1Club
    if (club.league && player1Club.league && club.league === player1Club.league) {
      weight *= SAME_LEAGUE_WEIGHT; // Increased multiplier for same league
    }

    // Ensure we don't select the same club twice
    if (club.name === player1Club.name) {
      weight = 0;
    }

    return weight;
  });

  // Select second club with weighted probability
  const player2Club = selectWeightedRandomClub(mainClubsList, player2Weights);

  // Log selection information for debugging
  console.log('Selected clubs:', {
    player1: {
      name: player1Club.name,
      league: player1Club.league,
      isTopLeague: isTopLeagueClub(player1Club)
    },
    player2: {
      name: player2Club.name,
      league: player2Club.league,
      isTopLeague: isTopLeagueClub(player2Club)
    },
    sameLeague: player1Club.league === player2Club.league
  });

  // Shuffle and select top teams from the regular clubs list
  const shuffledRegularClubs = shuffleArray(regularClubs);

  // Make sure we have enough teams for both players
  const teamsPerPlayer = Math.min(4, Math.floor(shuffledRegularClubs.length / 2));

  const player1TopTeams = shuffledRegularClubs.slice(0, teamsPerPlayer);
  const player2TopTeams = shuffledRegularClubs.slice(teamsPerPlayer, teamsPerPlayer * 2);

  const [score1, score2] = predictScore(player1Club, player2Club);

  return {
    player1Club,
    player2Club,
    player1TopTeams,
    player2TopTeams,
    score: {
      player1: score1,
      player2: score2
    },
    winner: score1 > score2 ? 1 : 2
  };
};