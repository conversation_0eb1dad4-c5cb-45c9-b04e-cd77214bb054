import React, { useState } from 'react';
import { QuizTranslations } from '../../types/quiz';

interface MultiPlayerSetupProps {
  onStart: (player1Name: string, player2Name: string) => void;
  onBack: () => void;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const MultiPlayerSetup: React.FC<MultiPlayerSetupProps> = ({ 
  onStart, 
  onBack, 
  t, 
  selectedLanguage 
}) => {
  const [player1Name, setPlayer1Name] = useState('');
  const [player2Name, setPlayer2Name] = useState('');

  const handleStart = () => {
    if (player1Name.trim() && player2Name.trim()) {
      onStart(player1Name.trim(), player2Name.trim());
    }
  };

  const isStartDisabled = !player1Name.trim() || !player2Name.trim();

  return (
    <div className="text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-6">{t('multiPlayer')}</h2>
      
      <div className="space-y-6 mb-8">
        {/* Player 1 Name Input */}
        <div>
          <label 
            htmlFor="player1Name" 
            className="block text-lg font-semibold text-gray-700 mb-2"
          >
            {t('player1Name')}
          </label>
          <input
            id="player1Name"
            type="text"
            value={player1Name}
            onChange={(e) => setPlayer1Name(e.target.value)}
            placeholder={t('player1NamePlaceholder')}
            className={`w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 transition-colors duration-200 ${
              selectedLanguage === 'ar' ? 'text-right' : 'text-left'
            }`}
            dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
            maxLength={20}
          />
        </div>

        {/* Player 2 Name Input */}
        <div>
          <label 
            htmlFor="player2Name" 
            className="block text-lg font-semibold text-gray-700 mb-2"
          >
            {t('player2Name')}
          </label>
          <input
            id="player2Name"
            type="text"
            value={player2Name}
            onChange={(e) => setPlayer2Name(e.target.value)}
            placeholder={t('player2NamePlaceholder')}
            className={`w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 transition-colors duration-200 ${
              selectedLanguage === 'ar' ? 'text-right' : 'text-left'
            }`}
            dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
            maxLength={20}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-4">
        <button
          onClick={handleStart}
          disabled={isStartDisabled}
          className={`w-full py-3 px-6 text-lg font-bold rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg ${
            isStartDisabled
              ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
              : 'gradient-yellow hover:shadow-xl'
          }`}
        >
          {t('startMultiPlayer')}
        </button>
        
        <button
          onClick={onBack}
          className="w-full bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out shadow-lg"
        >
          {t('back')}
        </button>
      </div>
    </div>
  );
};

export default MultiPlayerSetup;
