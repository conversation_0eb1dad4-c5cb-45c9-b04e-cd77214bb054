import React from 'react';
import { QuizTranslations } from '../../types/quiz';

interface LoadingScreenProps {
  t: (key: keyof QuizTranslations) => string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ t }) => {
  return (
    <div className="text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('loadingQuestions')}</h2>
      <p className="text-gray-600">{t('loadingQuestionsDesc')}</p>
      <div className="flex justify-center items-center mt-6">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    </div>
  );
};

export default LoadingScreen;
