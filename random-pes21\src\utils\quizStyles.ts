import { BackgroundImage } from '../types';

export const getContainerStyle = (background: BackgroundImage | null, useBackground: boolean) => {
  return (background && useBackground) ? {
    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${background.url})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  } : {};
};

export const getContainerClasses = (background: BackgroundImage | null, useBackground: boolean) => {
  return (background && useBackground)
    ? "min-h-screen flex items-center justify-center p-4 font-inter transition-all duration-500"
    : "min-h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-4 font-inter";
};
