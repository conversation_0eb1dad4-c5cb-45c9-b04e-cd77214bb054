import React from 'react';
import { QuizTranslations } from '../../types/quiz';

interface MultiPlayerComingSoonProps {
  onBack: () => void;
  t: (key: keyof QuizTranslations) => string;
}

const MultiPlayerComingSoon: React.FC<MultiPlayerComingSoonProps> = ({ onBack, t }) => {
  return (
    <div className="text-center mt-12">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('multiPlayer')}</h2>
      <p className="text-xl text-gray-700 mb-6">{t('comingSoon')}</p>
      <button
        onClick={onBack}
        className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-xl transition duration-300 ease-in-out shadow-lg"
      >
        {t('back')}
      </button>
    </div>
  );
};

export default MultiPlayerComingSoon;
