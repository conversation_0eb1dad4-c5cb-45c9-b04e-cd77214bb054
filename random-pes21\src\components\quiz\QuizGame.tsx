import React from 'react';
import { QuizQuestion, AnswerOption, QuizTranslations } from '../../types/quiz';

interface QuizGameProps {
  currentQuestion: number;
  totalQuestions: number;
  question: QuizQuestion;
  selectedAnswer: boolean | null;
  feedbackMessage: string;
  answersDisabled: boolean;
  explanation: string;
  loadingExplanation: boolean;
  onAnswerSelect: (isCorrect: boolean) => void;
  onExplainAnswer: () => void;
  onNextQuestion: () => void;
  onCancelQuiz: () => void;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const QuizGame: React.FC<QuizGameProps> = ({
  currentQuestion,
  totalQuestions,
  question,
  selectedAnswer,
  feedbackMessage,
  answersDisabled,
  explanation,
  loadingExplanation,
  onAnswerSelect,
  onExplainAnswer,
  onNextQuestion,
  onCancelQuiz,
  t,
  selectedLanguage
}) => {
  return (
    <div>
      <div className={`flex justify-between items-center mb-6 ${selectedLanguage === 'ar' ? 'flex-row-reverse' : ''}`}>
        <div className="text-lg font-semibold text-gray-600">
          {t('question')} <span className="font-bold text-blue-700">{currentQuestion + 1}</span> / {totalQuestions}
        </div>
        <button
          onClick={onCancelQuiz}
          className="bg-red-500 hover:bg-red-600 text-white text-sm font-bold py-2 px-4 rounded-xl transition duration-300 ease-in-out shadow-md"
        >
          {t('cancelQuiz')}
        </button>
      </div>
      
      <div className="text-2xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-4 shadow-inner">
        {question?.questionText}
      </div>
      
      <div className="space-y-4 mb-6">
        {question?.answerOptions.map((answerOption: AnswerOption, index: number) => (
          <button
            key={index}
            onClick={() => onAnswerSelect(answerOption.isCorrect)}
            className={`
              w-full ${selectedLanguage === 'ar' ? 'text-right' : 'text-left'} py-3 px-4 rounded-xl border-2
              transition duration-200 ease-in-out font-semibold text-lg
              ${
                selectedAnswer === null
                  ? 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-800'
                  : answerOption.isCorrect
                    ? 'bg-green-100 border-green-500 text-green-800'
                    : selectedAnswer !== null && !answerOption.isCorrect && selectedAnswer === false && selectedAnswer !== undefined
                      ? 'bg-red-100 border-red-500 text-red-800'
                      : 'bg-gray-100 border-gray-300 text-gray-800 opacity-70 cursor-not-allowed'
              }
              ${answersDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}
            `}
            disabled={answersDisabled}
          >
            {answerOption.answerText}
          </button>
        ))}
      </div>
      
      {feedbackMessage && (
        <div
          className={`text-center text-lg font-bold mb-4
            ${selectedAnswer ? 'text-green-600' : 'text-red-600'}
          `}
        >
          {feedbackMessage}
        </div>
      )}
      
      {selectedAnswer !== null && (
        <div className="mt-4">
          <button
            onClick={onExplainAnswer}
            className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
            disabled={loadingExplanation}
          >
            {loadingExplanation ? t('loadingExplanation') : t('explainAnswer')}
          </button>
          {explanation && (
            <div className="bg-gray-50 p-4 rounded-lg text-gray-700 text-base border border-gray-200" dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}>
              <h3 className="font-semibold text-base mb-2">{t('explanation')}</h3>
              <p>{explanation}</p>
            </div>
          )}
        </div>
      )}
      
      <button
        onClick={onNextQuestion}
        className={`w-full font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mt-6
          ${selectedAnswer !== null ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}
        `}
        disabled={selectedAnswer === null}
      >
        {currentQuestion === totalQuestions - 1 ? t('finishQuiz') : t('nextQuestion')}
      </button>
    </div>
  );
};

export default QuizGame;
