import { useState, useEffect } from 'react';
import Button from './components/Button';
import Table from './components/Table';
import BackgroundSelector from './components/BackgroundSelector';
import SettingsPanel from './components/SettingsPanel';
import { generateGameData } from './utils/gameLogic';
import { GameResult, BackgroundImage, Settings as SettingsType } from './types';
import { Volleyball, Settings as SettingsIcon } from 'lucide-react';
import { excludableTeams } from './data/clubs';
import { getMainClubs } from './data/mainClubs';
import './styles/theme.css';
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import Quiz from './components/Quiz';

function App() {
  const [gameData, setGameData] = useState<GameResult | null>(null);
  const [background, setBackground] = useState<BackgroundImage | null>(null);
  // Create initial excluded teams object with all teams set to false
  const initialExcludedTeams: Record<string, boolean> = {};
  excludableTeams.forEach(team => {
    initialExcludedTeams[team] = false;
  });

  const [settings, setSettings] = useState<SettingsType>({
    includeNationalTeams: false,
    excludedTeams: initialExcludedTeams
  });
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // State to store test results
  const [testResults, setTestResults] = useState<{
    weightedSameLeague: number;
    randomSameLeague: number;
    weightedTopLeagues: number;
    randomTopLeagues: number;
    total: number;
  } | null>(null);

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('gameSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleStart = () => {
    try {
      setErrorMessage(null);
      setGameData(generateGameData(settings));
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'An error occurred');
      console.error(error);
    }
  };

  // Function to run multiple simulations and collect statistics
  const runSimulations = (count: number) => {
    // Import the original shuffleArray function to create a pure random selection for comparison
    const shuffleArray = <T,>(array: T[]): T[] => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };

    // Function to simulate pure random selection (no weighting)
    const simulateRandomSelection = () => {
      const mainClubsList = getMainClubs();
      const shuffled = shuffleArray(mainClubsList);
      return {
        player1Club: shuffled[0],
        player2Club: shuffled[1]
      };
    };

    const topLeagues = ["🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League", "🇪🇸 La Liga", "🇮🇹 Serie A", "🇩🇪 Bundesliga", "🇫🇷 Ligue 1"];

    // Results for weighted selection
    const weightedResults = {
      sameLeague: 0,
      topLeagues: 0,
      total: count,
      leagueCounts: {} as Record<string, number>,
      sameLeagueCounts: {} as Record<string, number>
    };

    // Results for pure random selection
    const randomResults = {
      sameLeague: 0,
      topLeagues: 0,
      total: count,
      leagueCounts: {} as Record<string, number>,
      sameLeagueCounts: {} as Record<string, number>
    };

    for (let i = 0; i < count; i++) {
      // Test weighted selection
      const weighted = generateGameData(settings);
      const { player1Club, player2Club } = weighted;

      // Count leagues for weighted selection
      if (player1Club.league) {
        weightedResults.leagueCounts[player1Club.league] = (weightedResults.leagueCounts[player1Club.league] || 0) + 1;
      }
      if (player2Club.league) {
        weightedResults.leagueCounts[player2Club.league] = (weightedResults.leagueCounts[player2Club.league] || 0) + 1;
      }

      // Check if both clubs are from the same league (weighted)
      if (player1Club.league && player2Club.league && player1Club.league === player2Club.league) {
        weightedResults.sameLeague++;
        weightedResults.sameLeagueCounts[player1Club.league] = (weightedResults.sameLeagueCounts[player1Club.league] || 0) + 1;
      }

      // Check if both clubs are from top leagues (weighted)
      if (
        player1Club.league &&
        player2Club.league &&
        topLeagues.includes(player1Club.league) &&
        topLeagues.includes(player2Club.league)
      ) {
        weightedResults.topLeagues++;
      }

      // Test pure random selection
      const random = simulateRandomSelection();
      const { player1Club: randomPlayer1, player2Club: randomPlayer2 } = random;

      // Count leagues for random selection
      if (randomPlayer1.league) {
        randomResults.leagueCounts[randomPlayer1.league] = (randomResults.leagueCounts[randomPlayer1.league] || 0) + 1;
      }
      if (randomPlayer2.league) {
        randomResults.leagueCounts[randomPlayer2.league] = (randomResults.leagueCounts[randomPlayer2.league] || 0) + 1;
      }

      // Check if both clubs are from the same league (random)
      if (randomPlayer1.league && randomPlayer2.league && randomPlayer1.league === randomPlayer2.league) {
        randomResults.sameLeague++;
        randomResults.sameLeagueCounts[randomPlayer1.league] = (randomResults.sameLeagueCounts[randomPlayer1.league] || 0) + 1;
      }

      // Check if both clubs are from top leagues (random)
      if (
        randomPlayer1.league &&
        randomPlayer2.league &&
        topLeagues.includes(randomPlayer1.league) &&
        topLeagues.includes(randomPlayer2.league)
      ) {
        randomResults.topLeagues++;
      }
    }

    console.log('=== COMPARISON: WEIGHTED vs RANDOM ===');
    console.log(`Same league (weighted): ${weightedResults.sameLeague}/${count} (${(weightedResults.sameLeague/count*100).toFixed(2)}%)`);
    console.log(`Same league (random): ${randomResults.sameLeague}/${count} (${(randomResults.sameLeague/count*100).toFixed(2)}%)`);
    console.log(`Top leagues (weighted): ${weightedResults.topLeagues}/${count} (${(weightedResults.topLeagues/count*100).toFixed(2)}%)`);
    console.log(`Top leagues (random): ${randomResults.topLeagues}/${count} (${(randomResults.topLeagues/count*100).toFixed(2)}%)`);

    console.log('=== DETAILED STATS ===');
    console.log('Weighted - Same league counts:', weightedResults.sameLeagueCounts);
    console.log('Random - Same league counts:', randomResults.sameLeagueCounts);

    // Update the test results state
    setTestResults({
      weightedSameLeague: weightedResults.sameLeague,
      randomSameLeague: randomResults.sameLeague,
      weightedTopLeagues: weightedResults.topLeagues,
      randomTopLeagues: randomResults.topLeagues,
      total: count
    });

    return { weightedResults, randomResults };
  };

  const handleSettingsChange = (newSettings: SettingsType) => {
    setSettings(newSettings);
    localStorage.setItem('gameSettings', JSON.stringify(newSettings));
  };

  const containerStyle = background ? {
    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${background.url})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  } : {};

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={
          <div
            className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4 relative transition-all duration-500"
            style={containerStyle}
          >
            <BackgroundSelector onSelect={setBackground} />

            {/* Settings button in top right corner */}
            <div className="absolute top-4 right-4 z-10">
              <button
                className="gradient-yellow p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                onClick={() => setIsSettingsOpen(true)}
              >
                <SettingsIcon size={24} />
              </button>
            </div>

            <div className="text-center mb-8">
              <h1 className={`text-3xl md:text-4xl font-bold mb-2 gradient-red-blue inline-block text-transparent bg-clip-text ${
                background ? 'text-3xl md:text-4xl font-bold mb-2 gradient-red-blue inline-block text-transparent bg-clip-text glowy-white-shadow' : ''
              }`}>
                Bobboo vs Kyrillos Challenge!
              </h1>
              <p className={background ? "text-white" : "text-gray-600"}>
                Generate random 4 club matchups for PES 21 Random.
              </p>
            </div>

            <div className="flex gap-2 flex-wrap justify-center">
              <Button onClick={handleStart} icon={<Volleyball size={17} />}>
                Start
              </Button>
              <Button
                onClick={() => runSimulations(1000)}
                variant="secondary"
                title="Run 1000 simulations to test the weighted selection algorithm"
              >
                Test Weights
              </Button>
            </div>

            {errorMessage && (
              <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {errorMessage}
              </div>
            )}

            {gameData && !errorMessage && <Table gameData={gameData} />}

            {testResults && (
              <div className="mt-6 p-4 bg-white bg-opacity-90 rounded-lg shadow-lg max-w-2xl">
                <h2 className="text-xl font-bold mb-3">Test Results (Weighted vs Random)</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold">Same League Matches:</h3>
                    <p>Weighted: {testResults.weightedSameLeague}/{testResults.total} ({(testResults.weightedSameLeague/testResults.total*100).toFixed(2)}%)</p>
                    <p>Random: {testResults.randomSameLeague}/{testResults.total} ({(testResults.randomSameLeague/testResults.total*100).toFixed(2)}%)</p>
                    <p className="font-medium mt-1">
                      Improvement: {((testResults.weightedSameLeague - testResults.randomSameLeague)/testResults.total*100).toFixed(2)}%
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold">Top Leagues Matches:</h3>
                    <p>Weighted: {testResults.weightedTopLeagues}/{testResults.total} ({(testResults.weightedTopLeagues/testResults.total*100).toFixed(2)}%)</p>
                    <p>Random: {testResults.randomTopLeagues}/{testResults.total} ({(testResults.randomTopLeagues/testResults.total*100).toFixed(2)}%)</p>
                    <p className="font-medium mt-1">
                      Improvement: {((testResults.weightedTopLeagues - testResults.randomTopLeagues)/testResults.total*100).toFixed(2)}%
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Football Quiz button at the bottom */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
              <Link to="/quiz">
                <button
                  className="py-2 px-5 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center alternate-gradient-1"
                  style={{
                    minWidth: '140px',
                    cursor: 'pointer'
                  }}
                  title="Test your football knowledge"
                >
                  <span className="mr-2">🧠</span>
                  Football Quiz
                </button>
              </Link>
            </div>

            <SettingsPanel
              isOpen={isSettingsOpen}
              onClose={() => setIsSettingsOpen(false)}
              settings={settings}
              onSettingsChange={handleSettingsChange}
            />
          </div>
        } />
        <Route path="/quiz" element={<Quiz />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;