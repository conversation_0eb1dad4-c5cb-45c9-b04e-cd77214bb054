import React from 'react';
import { User, Users } from 'lucide-react';
import { QuizTranslations } from '../../types/quiz';

interface ModeSelectionProps {
  onModeSelect: (mode: string) => void;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const ModeSelection: React.FC<ModeSelectionProps> = ({ onModeSelect, t, selectedLanguage }) => {
  return (
    <div className="text-center mb-8">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('chooseMode')}</h2>
      <div className="flex flex-col gap-4 justify-center items-center">
        <button
          onClick={() => onModeSelect('Single Player')}
          className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center gradient-yellow"
          style={{ minWidth: '200px' }}
        >
          <User size={20} className={selectedLanguage === 'ar' ? 'ml-2' : 'mr-2'} />
          {t('singlePlayer')}
        </button>
        <button
          onClick={() => onModeSelect('Multi-Player')}
          className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center bg-gray-200 text-gray-800 hover:bg-gray-300"
          style={{ minWidth: '200px' }}
        >
          <Users size={20} className={selectedLanguage === 'ar' ? 'ml-2' : 'mr-2'} />
          {t('multiPlayer')}
        </button>
      </div>
    </div>
  );
};

export default ModeSelection;
