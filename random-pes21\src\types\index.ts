export interface Club {
  name: string;
  shortName?: string;
  strength?: number; // Added for match prediction
  league?: string; // Added for league information in tooltips
}

export interface GameResult {
  player1Club: Club;
  player2Club: Club;
  player1TopTeams: Club[];
  player2TopTeams: Club[];
  score: {
    player1: number;
    player2: number;
  };
  winner: 1 | 2;
}

export interface BackgroundImage {
  url: string;
  name: string;
}

export interface Settings {
  includeNationalTeams: boolean;
  excludedTeams: Record<string, boolean>;
}