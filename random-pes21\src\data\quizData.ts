import { Language, QuizTranslations } from '../types/quiz';

// Categories for the quiz
export const categories = {
  en: [
    'World Cup',
    'Champions League',
    'Player Trivia',
    'Football Rules',
    'Club Football',
    'International Football',
  ],
  ar: [
    'كأس العالم',
    'دوري أبطال أوروبا',
    'معلومات اللاعبين',
    'قوانين كرة القدم',
    'كرة القدم للأندية',
    'كرة القدم الدولية',
  ]
};

// Language options
export const languages: Language[] = [
  { code: 'en', name: 'English', flag: '🇬🇧' },
  { code: 'ar', name: 'العربية', flag: '🇪🇬' }
];

// Translations
export const translations: Record<string, QuizTranslations> = {
  en: {
    home: '← Home',
    testKnowledge: 'Test your football knowledge!',
    chooseMode: 'Choose Mode',
    singlePlayer: 'Single Player',
    multiPlayer: 'Multi-Player',
    comingSoon: 'Coming Soon 🚧',
    back: 'Back',
    backToSelectMode: 'Back to Select Mode',
    chooseCategory: 'Choose a Football Category',
    customCategory: 'Or Enter Your Own Category',
    customCategoryPlaceholder: "e.g., 'Premier League History'",
    startCustomQuiz: 'Start Custom Quiz 💪',
    loadingQuestions: 'Loading Questions...',
    loadingQuestionsDesc: 'Please wait while we fetch your trivia questions.',
    quizCompleted: 'Quiz Completed!',
    youScored: 'You scored',
    outOf: 'out of',
    playAgain: 'Play Again / Choose Category',
    backToHome: 'Back to Home',
    question: 'Question',
    cancelQuiz: 'Cancel Quiz',
    correct: 'Correct!',
    incorrect: 'Incorrect!',
    explainAnswer: '✨ Explain Answer',
    loadingExplanation: 'Loading Explanation...',
    explanation: 'Explanation:',
    nextQuestion: 'Next Question',
    finishQuiz: 'Finish Quiz',
    switchToArabic: 'Switch to Arabic',
    switchToEnglish: 'Switch to English',
    disableBackground: 'Disable background',
    enableBackground: 'Enable background',
    suggestCategory: 'Suggest a random football category',
    suggestingCategory: 'Getting suggestion...',
    victory: 'Victory!',
    // Multiplayer translations
    player1Name: 'Player 1 Name',
    player2Name: 'Player 2 Name',
    player1NamePlaceholder: 'Enter Player 1 name',
    player2NamePlaceholder: 'Enter Player 2 name',
    startMultiPlayer: 'Start Multiplayer Quiz',
    vs: 'Vs',
    winner: 'Winner',
    draw: 'Draw!',
    multiPlayerCompleted: 'Multiplayer Quiz Completed!'
  },
  ar: {
    home: '→ الرئيسية',
    testKnowledge: 'اختبر معرفتك بكرة القدم!',
    chooseMode: 'اختار وضع اللعب',
    singlePlayer: 'لعب فردى',
    multiPlayer: 'تحدى 1v1',
    comingSoon: 'قريباً 🚧',
    back: 'رجوع',
    backToSelectMode: 'العودة لاختيار وضع اللعب',
    chooseCategory: 'إختر موضوع الأسئلة',
    customCategory: 'أو أدخل الموضوع بنفسك ',
    customCategoryPlaceholder: 'مثال: "تاريخ الدوري الإنجليزي"',
    startCustomQuiz: 'ابدأ الاختبار 💪',
    loadingQuestions: 'جارى تحميل الأسئلة...',
    loadingQuestionsDesc: 'يرجى الانتظار بينما نجلب أسئلة الاختبار.',
    quizCompleted: 'تم إكمال الاختبار!',
    youScored: 'لقد حصلت على',
    outOf: 'من أصل',
    playAgain: 'العب مرة أخرى / اختر موضوع',
    backToHome: 'العودة للرئيسية',
    question: 'السؤال',
    cancelQuiz: 'إلغاء الاختبار',
    correct: 'صحيح!',
    incorrect: 'خطأ!',
    explainAnswer: '✨ إشرحلى الإجابة',
    loadingExplanation: 'جاري تحميل الشرح...',
    explanation: 'الشرح:',
    nextQuestion: 'السؤال التالى',
    finishQuiz: 'إنهاء الاختبار',
    switchToArabic: 'التبديل للعربية',
    switchToEnglish: 'التبديل للإنجليزية',
    disableBackground: 'إيقاف الخلفية',
    enableBackground: 'تفعيل الخلفية',
    suggestCategory: 'اقترح موضوع كرة قدم عشوائي',
    suggestingCategory: 'جاري الحصول على اقتراح...',
    victory: 'برافووو',
    // Multiplayer translations
    player1Name: 'اسم اللاعب الأول',
    player2Name: 'اسم اللاعب الثاني',
    player1NamePlaceholder: 'أدخل اسم اللاعب الأول',
    player2NamePlaceholder: 'أدخل اسم اللاعب الثاني',
    startMultiPlayer: 'ابدأ التحدي',
    vs: 'ضد',
    winner: 'الفائز',
    draw: 'تعادل!',
    multiPlayerCompleted: 'انتهى التحدي!'
  }
};
