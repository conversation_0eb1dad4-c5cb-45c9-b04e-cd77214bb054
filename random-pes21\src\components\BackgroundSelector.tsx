import React, { useRef, useEffect, useState } from 'react';
import { Image, Upload, Link, X } from 'lucide-react';
import { BackgroundImage } from '../types';
import { backgrounds } from '../data/backgrounds';

interface BackgroundSelectorProps {
  onSelect: (background: BackgroundImage | null) => void;
}

const BACKGROUND_STORAGE_KEY = 'selectedBackground';

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({ onSelect }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  useEffect(() => {
    // Load saved background from localStorage
    const savedBackground = localStorage.getItem(BACKGROUND_STORAGE_KEY);
    if (savedBackground) {
      onSelect(JSON.parse(savedBackground));
    }
  }, [onSelect]);

  const saveBackground = (background: BackgroundImage) => {
    // Save to localStorage for persistence across sessions and devices
    localStorage.setItem(BACKGROUND_STORAGE_KEY, JSON.stringify(background));
    onSelect(background);
  };

  const removeBackground = () => {
    localStorage.removeItem(BACKGROUND_STORAGE_KEY);
    onSelect(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const background = {
          name: 'Custom Background',
          url: e.target?.result as string
        };
        saveBackground(background);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUrlSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (imageUrl) {
      const background = {
        name: 'URL Background',
        url: imageUrl
      };
      saveBackground(background);
      setShowUrlInput(false);
      setImageUrl('');
    }
  };

  return (
    <div className="absolute top-4 left-4 z-10">
      <div className="dropdown">
        <button className="gradient-yellow p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
          <Image size={24} />
        </button>
        <div className="dropdown-content hidden group-hover:block absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-xl">
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="inline-block mr-2" size={16} />
            Upload Image
          </button>
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200"
            onClick={() => setShowUrlInput(!showUrlInput)}
          >
            <Link className="inline-block mr-2" size={16} />
            Add Image URL
          </button>
          {showUrlInput && (
            <form onSubmit={handleUrlSubmit} className="p-2 border-b border-gray-200">
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="Enter image URL"
                className="w-full p-2 border rounded mb-2 text-sm"
                required
              />
              <button
                type="submit"
                className="w-full px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Set Background
              </button>
            </form>
          )}
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200 text-red-500"
            onClick={removeBackground}
          >
            <X className="inline-block mr-2" size={16} />
            Remove Background
          </button>
          <div className="py-1 px-4 text-xs text-gray-500 border-b border-gray-200">Preset Backgrounds</div>
          {backgrounds.map((bg, index) => (
            <button
              key={index}
              className="block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200"
              onClick={() => saveBackground(bg)}
            >
              {bg.name}
            </button>
          ))}
        </div>
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileUpload}
      />
    </div>
  );
};

export default BackgroundSelector;