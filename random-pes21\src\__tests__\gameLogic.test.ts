import { generateGameData } from '../utils/gameLogic';

// Run multiple simulations to test the weighted selection
const runSimulations = (count: number) => {
  const results = {
    sameLeague: 0,
    topLeagues: 0,
    total: count
  };

  const topLeagues = ["🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League", "🇪🇸 La Liga", "🇮🇹 Serie A", "🇩🇪 Bundesliga", "🇫🇷 Ligue 1"];

  for (let i = 0; i < count; i++) {
    const gameData = generateGameData();
    const { player1Club, player2Club } = gameData;

    // Check if both clubs are from the same league
    if (player1Club.league && player2Club.league && player1Club.league === player2Club.league) {
      results.sameLeague++;
    }

    // Check if both clubs are from top leagues
    if (
      player1Club.league && 
      player2Club.league && 
      topLeagues.includes(player1Club.league) && 
      topLeagues.includes(player2Club.league)
    ) {
      results.topLeagues++;
    }
  }

  return results;
};

describe('Game Logic', () => {
  test('generateGameData should produce valid game data', () => {
    const gameData = generateGameData();
    
    expect(gameData).toBeDefined();
    expect(gameData.player1Club).toBeDefined();
    expect(gameData.player2Club).toBeDefined();
    expect(gameData.player1TopTeams.length).toBeGreaterThan(0);
    expect(gameData.player2TopTeams.length).toBeGreaterThan(0);
  });

  test('weighted selection should increase probability of same league and top leagues', () => {
    // This is a statistical test, so we need to run many simulations
    const simCount = 1000;
    const results = runSimulations(simCount);
    
    console.log(`Same league: ${results.sameLeague}/${simCount} (${(results.sameLeague/simCount*100).toFixed(2)}%)`);
    console.log(`Top leagues: ${results.topLeagues}/${simCount} (${(results.topLeagues/simCount*100).toFixed(2)}%)`);
    
    // We expect the percentage of same league matches to be higher than random chance
    // With random selection, the chance would be approximately 1/number of leagues
    // With our weighting, it should be higher
    
    // We also expect the percentage of top league matches to be higher than random chance
    // With random selection, the chance would be approximately (5/total leagues)^2
    // With our weighting, it should be higher
    
    // These are not strict tests, just verification that our weighting has an effect
    expect(results.sameLeague).toBeGreaterThan(0);
    expect(results.topLeagues).toBeGreaterThan(0);
  });
});
