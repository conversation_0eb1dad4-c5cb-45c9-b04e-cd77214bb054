import React from 'react';
import { Sparkles } from 'lucide-react';
import { QuizTranslations } from '../../types/quiz';

interface CategorySelectionProps {
  categories: string[];
  customCategoryInput: string;
  onCustomCategoryChange: (value: string) => void;
  onCategorySelect: (category: string) => void;
  onCustomCategorySubmit: () => void;
  onBackToModeSelect: () => void;
  onSuggestCategory: () => void;
  questionsError: string;
  loadingSuggestion: boolean;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const CategorySelection: React.FC<CategorySelectionProps> = ({
  categories,
  customCategoryInput,
  onCustomCategoryChange,
  onCategorySelect,
  onCustomCategorySubmit,
  onBackToModeSelect,
  onSuggestCategory,
  questionsError,
  loadingSuggestion,
  t,
  selectedLanguage
}) => {
  return (
    <div className="text-center">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('chooseCategory')}</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => onCategorySelect(category)}
            className="font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg text-yellow-100 border-yellow-300"
            style={{ background: 'linear-gradient(110deg, var(--barça-blue) 50%, var(--barça-red) 50%)' }}
          >
            {category}
          </button>
        ))}
      </div>
      <div className="mt-8">
        <h3 className="text-xl font-bold text-gray-800 mb-4">{t('customCategory')}</h3>
        <div className="relative mb-4">
          <input
            type="text"
            value={customCategoryInput}
            onChange={(e) => onCustomCategoryChange(e.target.value)}
            placeholder={t('customCategoryPlaceholder')}
            className={`w-full p-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 text-gray-800 ${
              selectedLanguage === 'ar' ? 'pr-12' : 'pl-3 pr-12'
            }`}
            dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
          />
          <button
            onClick={onSuggestCategory}
            disabled={loadingSuggestion}
            className={`absolute ${
              selectedLanguage === 'ar' ? 'left-3' : 'right-3'
            } top-1/2 transform -translate-y-1/2 p-2 text-purple-500 hover:text-purple-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed`}
            title={loadingSuggestion ? t('suggestingCategory') : t('suggestCategory')}
          >
            {loadingSuggestion ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500"></div>
            ) : (
              <Sparkles size={20} />
            )}
          </button>
        </div>
        <button
          onClick={onCustomCategorySubmit}
          className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2"
          disabled={!customCategoryInput.trim()}
        >
          {t('startCustomQuiz')}
        </button>
      </div>
      {questionsError && (
        <p className="text-red-500 mt-4 text-sm">{questionsError}</p>
      )}
      <button
        onClick={onBackToModeSelect}
        className="w-full mt-4 text-gray-700 font-bold py-2 px-6 rounded-xl transition duration-300"
      >
        {t('backToSelectMode')}
      </button>
    </div>
  );
};

export default CategorySelection;
