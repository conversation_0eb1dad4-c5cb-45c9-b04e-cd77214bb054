import { useState } from 'react';
import { QuizState } from '../types/quiz';

const initialQuizState: QuizState = {
  currentQuestion: 0,
  score: 0,
  showScore: false,
  selectedAnswer: null,
  feedbackMessage: '',
  answersDisabled: false,
  explanation: '',
  loadingExplanation: false,
  selectedCategory: null,
  quizQuestions: [],
  loadingQuestions: false,
  questionsError: '',
  customCategoryInput: '',
  selectedMode: null,
};

export function useQuizState() {
  const [state, setState] = useState<QuizState>(initialQuizState);

  const updateState = (updates: Partial<QuizState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const resetQuiz = () => {
    setState(initialQuizState);
  };

  const resetToCategories = () => {
    setState(prev => ({
      ...initialQuizState,
      selectedMode: prev.selectedMode, // Keep the selected mode
    }));
  };

  const handleAnswerSelection = (isCorrect: boolean, correctText: string, incorrectText: string) => {
    if (state.answersDisabled) return;

    updateState({
      selectedAnswer: isCorrect,
      answersDisabled: true,
      score: isCorrect ? state.score + 1 : state.score,
      feedbackMessage: isCorrect ? correctText : incorrectText,
    });
  };

  const moveToNextQuestion = () => {
    const nextQuestion = state.currentQuestion + 1;
    if (nextQuestion < state.quizQuestions.length) {
      updateState({
        currentQuestion: nextQuestion,
        selectedAnswer: null,
        feedbackMessage: '',
        answersDisabled: false,
        explanation: '',
      });
    } else {
      updateState({ showScore: true });
    }
  };

  return {
    state,
    updateState,
    resetQuiz,
    resetToCategories,
    handleAnswerSelection,
    moveToNextQuestion,
  };
}
