import React, { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { Settings } from '../types';
import { excludableTeams } from '../data/clubs';

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  settings: Settings;
  onSettingsChange: (settings: Settings) => void;
}

const SETTINGS_STORAGE_KEY = 'gameSettings';

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<Settings>(settings);

  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    let updatedSettings: Settings;

    // Handle team exclusion checkboxes
    if (name.startsWith('exclude_')) {
      const teamName = name.replace('exclude_', '');
      updatedSettings = {
        ...localSettings,
        excludedTeams: {
          ...localSettings.excludedTeams,
          [teamName]: checked
        }
      };
    } else {
      // Handle other settings
      updatedSettings = {
        ...localSettings,
        [name]: checked
      };
    }

    setLocalSettings(updatedSettings);
    onSettingsChange(updatedSettings);

    // Save to localStorage for persistence
    localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>

        <h2 className="text-2xl font-bold mb-6 gradient-red-blue inline-block text-transparent bg-clip-text">
          Game Settings
        </h2>

        <div className="space-y-6">
          <div className="flex items-start">
            <label htmlFor="includeNationalTeams" className="text-gray-700 mr-2" dir="rtl">
              <span className="font-medium">إضافة المنتخبات الوطنية</span>
              <div className="text-sm text-gray-500 mt-1">
                🇩🇪 المانيا ~ 🇪🇸 اسبانيا ~ 🇦🇷 الأرجنتين ~ 🇧🇷 البرازيل ~ 🇫🇷 فرنسا ~ 🇵🇹 البرتغال ~ 🏴󠁧󠁢󠁥󠁮󠁧󠁿 إنجلترا ~ 🇳🇱 هولندا
              </div>
            </label>
            <input
              type="checkbox"
              id="includeNationalTeams"
              name="includeNationalTeams"
              checked={localSettings.includeNationalTeams}
              onChange={handleChange}
              className="h-7 w-7 text-blue-600 rounded focus:ring-blue-500 mt-1 mr-3"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-start">
              <label className="text-gray-700 mr-2 w-full" dir="rtl">
                <span className="font-medium">إستبعاد بعض الفرق</span>
                <div className="text-sm text-gray-500 mt-1">
                  إختر الفرق التى تريد إستبعادها من القائمة.
                </div>
              </label>
            </div>

            <div className="grid grid-cols-2 gap-2 mt-2">
              {excludableTeams.map(team => (
                <div key={team} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`exclude_${team}`}
                    name={`exclude_${team}`}
                    checked={!!localSettings.excludedTeams?.[team]}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500 mr-2"
                  />
                  <label htmlFor={`exclude_${team}`} className="text-sm text-gray-700">
                    {team}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-8 flex justify-center">
          <button
            onClick={onClose}
            className="gradient-yellow py-2 px-4 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300"
          >
            Ok
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
