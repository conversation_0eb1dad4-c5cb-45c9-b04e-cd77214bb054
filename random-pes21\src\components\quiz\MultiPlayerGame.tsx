import React from 'react';
import { QuizQuestion, AnswerOption, QuizTranslations, PlayerState } from '../../types/quiz';

interface MultiPlayerGameProps {
  player1: PlayerState;
  player2: PlayerState;
  onPlayer1Answer: (isCorrect: boolean, answerText: string) => void;
  onPlayer2Answer: (isCorrect: boolean, answerText: string) => void;
  onPlayer1Next: () => void;
  onPlayer2Next: () => void;
  onCancelQuiz: () => void;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const MultiPlayerGame: React.FC<MultiPlayerGameProps> = ({
  player1,
  player2,
  onPlayer1Answer,
  onPlayer2Answer,
  onPlayer1Next,
  onPlayer2Next,
  onCancelQuiz,
  t,
  selectedLanguage
}) => {
  const renderPlayerQuiz = (
    player: PlayerState,
    onAnswer: (isCorrect: boolean, answerText: string) => void,
    onNext: () => void,
    isPlayer1: boolean
  ) => {
    const currentQuestion = player.quizQuestions[player.currentQuestion];
    
    if (!currentQuestion) return null;

    return (
      <div className="bg-white rounded-3xl shadow-2xl p-6 w-full border-4 border-yellow-300">
        {/* Player Name Header */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">{player.name}</h3>
          <h4 className="text-xl font-extrabold gradient-red-blue inline-block text-transparent bg-clip-text">
            Football Quiz
          </h4>
        </div>

        {/* Question Progress */}
        <div className={`flex justify-between items-center mb-4 ${selectedLanguage === 'ar' ? 'flex-row-reverse' : ''}`}>
          <div className="text-sm font-semibold text-gray-600">
            {t('question')} <span className="font-bold text-blue-700">{player.currentQuestion + 1}</span> / {player.quizQuestions.length}
          </div>
          <div className="text-sm font-semibold text-gray-600">
            Score: <span className="font-bold text-purple-700">{player.score}</span>
          </div>
        </div>

        {/* Question Text */}
        <div className="text-lg font-bold text-gray-800 mb-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-3 shadow-inner">
          {currentQuestion.questionText}
        </div>

        {/* Answer Options */}
        <div className="space-y-3 mb-4">
          {currentQuestion.answerOptions.map((answerOption: AnswerOption, index: number) => (
            <button
              key={index}
              onClick={() => onAnswer(answerOption.isCorrect, answerOption.answerText)}
              className={`
                w-full ${selectedLanguage === 'ar' ? 'text-right' : 'text-left'} py-2 px-3 rounded-lg border-2
                transition duration-200 ease-in-out font-semibold text-sm
                ${
                  player.selectedAnswer === null
                    ? 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-800 cursor-pointer'
                    : 'bg-gray-100 border-gray-300 text-gray-800 opacity-70 cursor-not-allowed'
                }
                ${player.answersDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}
              `}
              disabled={player.answersDisabled}
            >
              {answerOption.answerText}
            </button>
          ))}
        </div>

        {/* Next Question Button */}
        <button
          onClick={onNext}
          className={`w-full font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 shadow-lg text-sm
            ${player.selectedAnswer !== null ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}
          `}
          disabled={player.selectedAnswer === null}
        >
          {player.currentQuestion === player.quizQuestions.length - 1 ? t('finishQuiz') : t('nextQuestion')}
        </button>
      </div>
    );
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      {/* Cancel Quiz Button */}
      <div className="flex justify-center mb-6">
        <button
          onClick={onCancelQuiz}
          className="bg-red-500 hover:bg-red-600 text-white text-sm font-bold py-2 px-4 rounded-xl transition duration-300 ease-in-out shadow-md"
        >
          {t('cancelQuiz')}
        </button>
      </div>

      {/* Two Quiz Components Side by Side */}
      <div className="flex gap-6 items-start justify-center">
        {/* Player 1 Quiz */}
        <div className="flex-1 max-w-md">
          {renderPlayerQuiz(player1, onPlayer1Answer, onPlayer1Next, true)}
        </div>

        {/* VS Text */}
        <div className="flex items-center justify-center px-4 py-8">
          <div className="gradient-yellow text-black font-bold text-3xl px-6 py-3 rounded-full shadow-lg">
            {t('vs')}
          </div>
        </div>

        {/* Player 2 Quiz */}
        <div className="flex-1 max-w-md">
          {renderPlayerQuiz(player2, onPlayer2Answer, onPlayer2Next, false)}
        </div>
      </div>
    </div>
  );
};

export default MultiPlayerGame;
