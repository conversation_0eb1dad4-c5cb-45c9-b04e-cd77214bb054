import React from 'react';
import { Link } from 'react-router-dom';
import { Trophy, PartyPopper } from 'lucide-react';
import { QuizTranslations, PlayerState } from '../../types/quiz';

interface MultiPlayerResultsProps {
  player1: PlayerState;
  player2: PlayerState;
  onPlayAgain: () => void;
  t: (key: keyof QuizTranslations) => string;
  selectedLanguage: string;
}

const MultiPlayerResults: React.FC<MultiPlayerResultsProps> = ({
  player1,
  player2,
  onPlayAgain,
  t,
  selectedLanguage
}) => {
  const getWinner = () => {
    if (player1.score > player2.score) {
      return { winner: player1.name, isDraw: false };
    } else if (player2.score > player1.score) {
      return { winner: player2.name, isDraw: false };
    } else {
      return { winner: null, isDraw: true };
    }
  };

  const { winner, isDraw } = getWinner();
  const totalQuestions = player1.quizQuestions.length;

  return (
    <div className="text-center w-full max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold text-gray-800 mb-6">{t('multiPlayerCompleted')}</h2>
      
      {/* Winner/Draw Announcement */}
      <div className="mb-8">
        {isDraw ? (
          <div className="mb-6">
            <div className="flex items-center justify-center mb-4">
              <PartyPopper size={48} className="text-blue-500 mr-3 ml-3 fill-blue-100" />
              <h3 className="text-4xl font-bold text-blue-500">{t('draw')}</h3>
              <PartyPopper size={48} className="text-blue-500 mr-3 ml-3 fill-blue-100" />
            </div>
            <p className="text-xl text-gray-700">
              {t('youScored')} <span className="font-bold text-blue-600">{player1.score}</span> - <span className="font-bold text-blue-600">{player2.score}</span>
            </p>
          </div>
        ) : (
          <div className="mb-6">
            <div className="flex items-center justify-center mb-4">
              <Trophy size={48} className="text-yellow-500 mr-3 ml-3 fill-yellow-100" />
              <h3 className="text-4xl font-bold text-yellow-500">{t('winner')}: {winner}</h3>
              <Trophy size={48} className="text-yellow-500 mr-3 ml-3 fill-yellow-100" />
            </div>
          </div>
        )}
      </div>

      {/* Detailed Scores */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Player 1 Score */}
        <div className="bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl p-6 shadow-lg">
          <h4 className="text-xl font-bold text-gray-800 mb-2">{player1.name}</h4>
          <p className="text-lg text-gray-700">
            {t('youScored')} <span className="font-bold text-purple-700">{player1.score}</span> {t('outOf')} <span className="font-bold text-purple-700">{totalQuestions}</span>
          </p>
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-purple-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${(player1.score / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Player 2 Score */}
        <div className="bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-6 shadow-lg">
          <h4 className="text-xl font-bold text-gray-800 mb-2">{player2.name}</h4>
          <p className="text-lg text-gray-700">
            {t('youScored')} <span className="font-bold text-blue-700">{player2.score}</span> {t('outOf')} <span className="font-bold text-blue-700">{totalQuestions}</span>
          </p>
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${(player2.score / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-4">
        <button
          onClick={onPlayAgain}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg"
        >
          {t('playAgain')}
        </button>
        
        <Link 
          to="/" 
          className="w-full block bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2 border-yellow-500"
        >
          {t('backToHome')}
        </Link>
      </div>
    </div>
  );
};

export default MultiPlayerResults;
