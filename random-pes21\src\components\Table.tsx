import React, { useState } from 'react';
import { GameResult } from '../types';
import { Trophy, Medal } from 'lucide-react';

interface TableProps {
  gameData: GameResult;
}

const Table: React.FC<TableProps> = ({ gameData }) => {
  const { player1Club, player2Club, player1TopTeams, player2TopTeams, score, winner } = gameData;
  const [activeTooltip, setActiveTooltip] = useState<string | null>(null);

  // Function to handle tooltip toggle for mobile
  const handleTooltipToggle = (tooltipId: string) => {
    if (activeTooltip === tooltipId) {
      setActiveTooltip(null);
    } else {
      setActiveTooltip(tooltipId);
    }
  };

  // Function to render club name with tooltip
  const renderClubWithTooltip = (club: typeof player1Club, tooltipId: string) => {
    return (
      <span className="relative group">
        <span
          className={`text-yellow-400 ${club.league ? 'cursor-pointer underline-offset-4' : ''}`}
          onClick={() => club.league && handleTooltipToggle(tooltipId)}
        >
          {club.name}
        </span>

        {club.league && (
          <>
            {/* Desktop tooltip (hover) */}
            <span className="hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 -mt-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
              {club.league}
            </span>

            {/* Mobile tooltip (click/tap) */}
            {activeTooltip === tooltipId && (
              <span className="md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 -mt-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                {club.league}
              </span>
            )}
          </>
        )}
      </span>
    );
  };

  return (
    <div className="fade-in w-full max-w-2xl mx-auto mt-8 overflow-hidden rounded-lg shadow-lg">
      <table className="w-full border-collapse">
        <thead>
          <tr className="gradient-red-blue">
            <th className="py-3 px-4 text-left">
              Bobboo ~ {renderClubWithTooltip(player1Club, 'player1-club')}
            </th>
            <th className="py-3 px-4 text-left">
              Kyrillos ~ {renderClubWithTooltip(player2Club, 'player2-club')}
            </th>
          </tr>
        </thead>
        <tbody>
          {player1TopTeams.map((team, index) => (
            <tr key={index} className={index % 2 === 0 ? "alternate-gradient-1" : "alternate-gradient-2"}>
              <td className="py-3 px-4">
                <span className="font-bold mr-0.5">{index + 1}</span>-  {team.name}
              </td>
              <td className="py-3 px-4">
                {player2TopTeams[index] ? (
                  <>
                    <span className="font-bold mr-0.5">{index + 1}</span>-  {player2TopTeams[index].name}
                  </>
                ) : (
                  <span className="text-gray-400">No team available</span>
                )}
              </td>
            </tr>
          ))}
          <tr className="gradient-yellow">
            <td colSpan={2} className="py-3 px-4 font-bold">
              <div className="flex items-center">
                <span className="text-black mr-2 relative top-3 font-bold text-lg">Result:</span>
                <div className="flex items-center flex-1 justify-center">
                  <span className="text-black relative group">
                    <span
                      className={`${player1Club.league ? 'cursor-pointer underline-offset-2' : ''}`}
                      onClick={() => player1Club.league && handleTooltipToggle('result-player1')}
                    >
                      {player1Club.shortName || player1Club.name}
                    </span>
                    {player1Club.league && (
                      <>
                        {/* Desktop tooltip */}
                        <span className="hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                          {player1Club.league}
                        </span>

                        {/* Mobile tooltip */}
                        {activeTooltip === 'result-player1' && (
                          <span className="md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                            {player1Club.league}
                          </span>
                        )}
                      </>
                    )}
                  </span>
                  <span className="text-black ml-2"> {score.player1}</span>
                  <span className="text-black mx-2">–</span>
                  <span className="text-black mr-2">{score.player2} </span>
                  <span className="text-black relative group">
                    <span
                      className={`${player2Club.league ? 'cursor-pointer underline-offset-2' : ''}`}
                      onClick={() => player2Club.league && handleTooltipToggle('result-player2')}
                    >
                      {player2Club.shortName || player2Club.name}
                    </span>
                    {player2Club.league && (
                      <>
                        {/* Desktop tooltip */}
                        <span className="hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                          {player2Club.league}
                        </span>

                        {/* Mobile tooltip */}
                        {activeTooltip === 'result-player2' && (
                          <span className="md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                            {player2Club.league}
                          </span>
                        )}
                      </>
                    )}
                  </span>
                </div>
                <span className="ml-2">
                  <Trophy size={25} className="ml-2 mr-2 relative top-3" />
                </span>
              </div>
              <p className="text-black text-sm text-center mt-1 flex justify-center">
                <Medal size={17} className="inline mr-1 ml-3" />
                {winner === 1 ? "Bobboo" : "Kyrillos"} is the winner!
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default Table;