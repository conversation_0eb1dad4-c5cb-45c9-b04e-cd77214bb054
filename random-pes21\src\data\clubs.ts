import { Club, Settings } from '../types';
import { nationalTeams } from './nationalTeams';

export const clubTeams: Club[] = [
  { name: "FC Barcelona", shortName: "Barça", strength: 88 },
  { name: "Real Madrid", shortName: "Madrid", strength: 87 },
  { name: "Bayern Munich", shortName: "Bayern", strength: 86 },
  { name: "Manchester United", shortName: "Man Utd", strength: 83 },
  { name: "Liverpool", shortName: "Liverpool", strength: 85 },
  { name: "Chelsea", shortName: "Chelsea", strength: 82 },
  { name: "Manchester City", shortName: "Man City", strength: 88 },
  { name: "Paris Saint-Germain", shortName: "PSG", strength: 85 },
  { name: "Juventus", shortName: "Juventus", strength: 81 },
  { name: "AC Milan", shortName: "Milan", strength: 80 },
  { name: "Inter Milan", shortName: "Inter", strength: 82 },
  { name: "Arsenal", shortName: "Arsenal", strength: 84 },
  { name: "Borussia Dortmund", shortName: "Dortmund", strength: 81 },
  { name: "Atletico Madrid", shortName: "Atletico", strength: 83 },
  { name: "Tottenham Hotspur", shortName: "Spurs", strength: 82 },
  { name: "Ajax", shortName: "Ajax", strength: 79 },
  { name: "Napoli", shortName: "Napoli", strength: 82 },
  { name: "Benfica", shortName: "Benfica", strength: 78 },
  { name: "Porto", shortName: "Porto", strength: 77 },
  { name: "AS Roma", shortName: "Roma", strength: 79 }
];

// List of teams that can be excluded
export const excludableTeams = [
  "Real Madrid",
  "Paris Saint-Germain",
  "Bayern Munich",
  "Tottenham Hotspur",
  "Juventus",
  "Ajax",
  "Napoli",
  "AS Roma",
  "Benfica",
  "Borussia Dortmund",
  "Atletico Madrid",
  "Porto",
  "AC Milan",
  "Inter Milan",
  "Chelsea",
  "Manchester United"
];

// Function to get all clubs based on settings
export const getClubs = (settings?: Settings): Club[] => {
  let filteredClubs = [...clubTeams];

  // Filter out teams based on excludedTeams settings
  if (settings?.excludedTeams) {
    filteredClubs = filteredClubs.filter(club => !settings.excludedTeams[club.name]);
  }

  // Add national teams if setting is enabled
  if (settings?.includeNationalTeams) {
    return [...filteredClubs, ...nationalTeams];
  }

  return filteredClubs;
};

// For backward compatibility
export const clubs = clubTeams;