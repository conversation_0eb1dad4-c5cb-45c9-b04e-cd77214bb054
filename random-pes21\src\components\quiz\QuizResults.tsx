import React from 'react';
import { Link } from 'react-router-dom';
import { QuizTranslations } from '../../types/quiz';

interface QuizResultsProps {
  score: number;
  totalQuestions: number;
  onPlayAgain: () => void;
  t: (key: keyof QuizTranslations) => string;
}

const QuizResults: React.FC<QuizResultsProps> = ({ score, totalQuestions, onPlayAgain, t }) => {
  return (
    <div className="text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('quizCompleted')}</h2>
      <p className="text-xl text-gray-700 mb-6">
        {t('youScored')} <span className="font-bold text-purple-700">{score}</span> {t('outOf')} <span className="font-bold text-purple-700">{totalQuestions}</span>
      </p>
      <button
        onClick={onPlayAgain}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
      >
        {t('playAgain')}
      </button>
      <Link 
        to="/" 
        className="w-full block bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2 border-yellow-500"
      >
        {t('backToHome')}
      </Link>
    </div>
  );
};

export default QuizResults;
